package fr.enedis.i2r.infra.systemd;

import fr.enedis.i2r.system.ports.WatchdogSocketPort;

public class WatchdogSocketAdapter implements WatchdogSocketPort {

    private UnixSocket socket;

    public WatchdogSocketAdapter(String socket) throws InvalidSocketTypeException {
        this.socket = new UnixSocket(socket);
    }

    @Override
    public void init() {
        socket.notify("READY=1");
    }

    @Override
    public void heartbeat() {
        socket.notify("WATCHDOG=1");
    }

    @Override
    public void heartbeatWithMessage(String message) {
        socket.notify("WATCHDOG=1\nSTATUS=" + message);
    }
}
