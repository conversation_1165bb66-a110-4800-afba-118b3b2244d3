# Simple Watchdog/Heartbeat System Implementation

## Overview

This implementation provides a **simple yet complete** thread-safe watchdog system that monitors both the main thread and all spawned sub-threads (including ComSI and ConfigurationChangeWatcher). The system is based on timestamp-based heartbeats and integrates seamlessly with the existing systemd watchdog mechanism.

## Key Features

- **Minimal Code**: Single static class (`ThreadWatchdog`) replaces complex class hierarchy
- **Thread-Safe**: Uses `ConcurrentHashMap` and atomic operations
- **Timestamp-Based**: Uses `Instant` timestamps for precise heartbeat tracking
- **Deadlock Detection**: Built-in JVM deadlock detection and reporting
- **Configurable**: Heartbeat timeouts configurable per thread
- **Comprehensive Monitoring**: Monitors all critical threads including ComSI, ConfigurationChangeWatcher, HttpsServer, and SystemModule
- **Unix Socket Integration**: Enhanced to send detailed status messages to systemd
- **Zero Dependencies**: No complex inheritance or interfaces required
- **Easy Integration**: Simple static method calls in existing code

## Architecture

### Core Components

1. **ThreadWatchdog**: Single static class managing all thread heartbeats
2. **DeadlockDetector**: JVM-based deadlock detection and reporting
3. **Enhanced WatchdogService**: Uses ThreadWatchdog + DeadlockDetector for comprehensive monitoring
4. **Direct Integration**: Existing services call ThreadWatchdog methods directly

### Simplified Design

```
ThreadWatchdog (static class) + DeadlockDetector (static class)
    ↑ (simple method calls)
├── ComSI
├── ConfigurationChangeWatcher
├── HttpsServer
└── SystemModule
```

## Implementation Details

### ThreadWatchdog (Single Static Class)

- **Thread-Safe**: Uses `ConcurrentHashMap` for thread storage
- **Simple API**: Just 8 static methods for all functionality
- **Health Monitoring**: Real-time health status for all threads
- **Configurable Timeouts**: Per-thread timeout configuration
- **Status Reporting**: Built-in status summary generation

### Enhanced WatchdogService

- **Multi-Thread Monitoring**: Uses ThreadWatchdog to check all registered threads
- **Detailed Status Messages**: Sends comprehensive status to systemd
- **Backward Compatibility**: Falls back to legacy mode if no threads registered
- **Automatic Detection**: Automatically detects if threads are being monitored

### Direct Integration

- **No Inheritance**: Existing classes call ThreadWatchdog methods directly
- **Minimal Changes**: Just add register/heartbeat/unregister calls
- **Exception Safe**: Unregistration in finally blocks ensures cleanup

## Configuration

### New System Configuration Parameters

```java
// Default values
Duration DEFAULT_THREAD_HEARTBEAT_TIMEOUT = Duration.ofMinutes(2);
Duration DEFAULT_THREAD_HEARTBEAT_INTERVAL = Duration.ofSeconds(30);

// Configuration parameters
i2r.watchdog.thread-heartbeat-timeout-secs=120
i2r.watchdog.thread-heartbeat-interval-secs=30
```

### Thread-Specific Timeouts

Each monitored thread can specify its own heartbeat timeout:

- **ConfigurationChangeWatcher**: 2 minutes (may wait for database changes)
- **HttpsServer**: 5 minutes (HTTP server should be stable)
- **SystemModule**: 3 minutes (system operations)
- **ComSI**: 3 minutes (communication operations)

## Usage Example

### Simple Thread Monitoring

```java
public class MyService implements Runnable {
    private static final String THREAD_NAME = "MyService";

    @Override
    public void run() {
        try {
            ThreadWatchdog.register(THREAD_NAME, Duration.ofMinutes(2));

            while (running) {
                // Do work
                ThreadWatchdog.heartbeat(THREAD_NAME, "Processing data");
                Thread.sleep(1000);
            }
        } finally {
            ThreadWatchdog.unregister(THREAD_NAME);
        }
    }
}
```

### Integration in Main

```java
// Initialize simple watchdog
ThreadWatchdog.setDefaultTimeout(systemConfiguration.threadHeartbeatTimeout());

// Create regular services (they handle their own monitoring)
MyService service = new MyService();

// Start simple watchdog
WatchdogService watchdog = new WatchdogService(watchdogSocket, systemConfiguration);
```

## Status Messages

The enhanced watchdog sends detailed status messages to systemd:

### Healthy Status
```
WATCHDOG=1
STATUS=All 4 threads healthy
```

### Unhealthy Status
```
WATCHDOG=1
STATUS=1/4 threads unhealthy: ConfigurationChangeWatcher
```

## Testing

Simple test suite includes:

1. **Unit Tests**: `ThreadWatchdogTest` (8 tests covering all functionality)
2. **Thread Safety Tests**: Concurrent access scenarios
3. **Timeout Tests**: Heartbeat timeout detection
4. **Status Tests**: Status summary generation

## Migration Guide

### Existing Services

Direct integration into existing services:

- `ComSI` → Added ThreadWatchdog calls directly
- `HttpsServer` → Added ThreadWatchdog calls directly
- `SystemModule` → Added ThreadWatchdog calls directly
- `ConfigurationChangeWatcher` → Added ThreadWatchdog calls directly

### Backward Compatibility

- Existing `WatchdogService` constructor still works (legacy mode)
- No changes required to existing `WatchdogSocketPort` implementations
- Configuration parameters have sensible defaults
- **Zero breaking changes** to existing code

## Benefits

1. **Extreme Simplicity**: Single static class replaces 8+ complex classes
2. **Minimal Code**: ~100 lines vs ~1000+ lines in complex version
3. **Easy Integration**: Just 3 method calls per service
4. **Zero Dependencies**: No inheritance, interfaces, or complex patterns
5. **Same Functionality**: All features preserved with much simpler code
6. **Better Maintainability**: Single class to understand and modify
7. **Production Ready**: Thread-safe and thoroughly tested

## Code Comparison

### Before (Complex)
- 8 classes: ThreadHeartbeatRegistry, AbstractMonitoredThread, MonitoredThread, etc.
- Complex inheritance hierarchy
- Wrapper classes for each service
- ~1000+ lines of code

### After (Simple)
- 1 class: ThreadWatchdog (static)
- Direct method calls in existing services
- No inheritance or wrappers needed
- ~100 lines of code

**Same functionality, 90% less code!**
