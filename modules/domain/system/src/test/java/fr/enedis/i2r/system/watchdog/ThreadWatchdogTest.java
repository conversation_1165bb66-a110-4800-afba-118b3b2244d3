package fr.enedis.i2r.system.watchdog;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class ThreadWatchdogTest {

    @BeforeEach
    void setUp() {
        // Clear any existing threads before each test
        ThreadWatchdog.setDefaultTimeout(Duration.ofSeconds(5));
    }

    @Test
    void testRegisterAndHeartbeat() {
        String threadName = "test-thread";
        
        ThreadWatchdog.register(threadName);
        ThreadWatchdog.heartbeat(threadName);
        
        assertEquals(1, ThreadWatchdog.getThreadCount());
        assertFalse(ThreadWatchdog.hasUnhealthyThreads());
        
        ThreadWatchdog.unregister(threadName);
        assertEquals(0, ThreadWatchdog.getThreadCount());
    }

    @Test
    void testRegisterWithCustomTimeout() {
        String threadName = "custom-timeout-thread";
        Duration customTimeout = Duration.ofSeconds(10);
        
        ThreadWatchdog.register(threadName, customTimeout);
        ThreadWatchdog.heartbeat(threadName, "Custom status");
        
        assertEquals(1, ThreadWatchdog.getThreadCount());
        String status = ThreadWatchdog.getThreadStatus(threadName);
        assertTrue(status.contains("Healthy"));
        assertTrue(status.contains("Custom status"));
        
        ThreadWatchdog.unregister(threadName);
    }

    @Test
    void testUnhealthyThreadDetection() throws InterruptedException {
        String threadName = "unhealthy-thread";
        Duration shortTimeout = Duration.ofMillis(100);
        
        ThreadWatchdog.register(threadName, shortTimeout);
        ThreadWatchdog.heartbeat(threadName);
        
        // Wait for timeout
        Thread.sleep(150);
        
        assertTrue(ThreadWatchdog.hasUnhealthyThreads());
        Set<String> unhealthyThreads = ThreadWatchdog.getUnhealthyThreads();
        assertTrue(unhealthyThreads.contains(threadName));
        
        String status = ThreadWatchdog.getThreadStatus(threadName);
        assertTrue(status.contains("Unhealthy"));
        
        ThreadWatchdog.unregister(threadName);
    }

    @Test
    void testStatusSummary() {
        ThreadWatchdog.register("thread1");
        ThreadWatchdog.register("thread2");
        ThreadWatchdog.heartbeat("thread1");
        ThreadWatchdog.heartbeat("thread2");
        
        String summary = ThreadWatchdog.getStatusSummary();
        assertTrue(summary.contains("All 2 threads healthy"));
        
        ThreadWatchdog.unregister("thread1");
        ThreadWatchdog.unregister("thread2");
    }

    @Test
    void testStatusSummaryWithUnhealthyThreads() throws InterruptedException {
        ThreadWatchdog.register("healthy-thread");
        ThreadWatchdog.register("unhealthy-thread", Duration.ofMillis(50));
        
        ThreadWatchdog.heartbeat("healthy-thread");
        ThreadWatchdog.heartbeat("unhealthy-thread");
        
        // Wait for one thread to become unhealthy
        Thread.sleep(100);
        
        String summary = ThreadWatchdog.getStatusSummary();
        assertTrue(summary.contains("1/2 threads unhealthy"));
        assertTrue(summary.contains("unhealthy-thread"));
        
        ThreadWatchdog.unregister("healthy-thread");
        ThreadWatchdog.unregister("unhealthy-thread");
    }

    @Test
    void testHeartbeatForUnregisteredThread() {
        // This should not throw an exception
        assertDoesNotThrow(() -> ThreadWatchdog.heartbeat("unregistered-thread"));
    }

    @Test
    void testMultipleThreads() {
        for (int i = 0; i < 5; i++) {
            ThreadWatchdog.register("thread-" + i);
            ThreadWatchdog.heartbeat("thread-" + i, "Status " + i);
        }
        
        assertEquals(5, ThreadWatchdog.getThreadCount());
        assertFalse(ThreadWatchdog.hasUnhealthyThreads());
        
        String summary = ThreadWatchdog.getStatusSummary();
        assertTrue(summary.contains("All 5 threads healthy"));
        
        // Clean up
        for (int i = 0; i < 5; i++) {
            ThreadWatchdog.unregister("thread-" + i);
        }
        
        assertEquals(0, ThreadWatchdog.getThreadCount());
    }

    @Test
    void testSetDefaultTimeout() {
        Duration newTimeout = Duration.ofMinutes(1);
        ThreadWatchdog.setDefaultTimeout(newTimeout);
        
        ThreadWatchdog.register("test-thread");
        ThreadWatchdog.heartbeat("test-thread");
        
        // The timeout should be applied (we can't easily test this without waiting)
        assertEquals(1, ThreadWatchdog.getThreadCount());
        
        ThreadWatchdog.unregister("test-thread");
    }
}
