package fr.enedis.i2r.system;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

import ch.qos.logback.classic.Level;

public record SystemConfiguration(
    Level logLevel,
    List<String> secondaryServices,
    Duration watchdogPeriod,
    Duration threadHeartbeatTimeout,
    Duration threadHeartbeatInterval) {

    public static final Level DEFAULT_LOG_LEVEL = Level.INFO;
    public static final Duration DEFAULT_WATCHDOG_INTERVAL_SECS = Duration.ofSeconds(45);
    public static final Duration DEFAULT_THREAD_HEARTBEAT_TIMEOUT = Duration.ofMinutes(2);
    public static final Duration DEFAULT_THREAD_HEARTBEAT_INTERVAL = Duration.ofSeconds(30);
    public static final List<String> DEFAULT_SECONDARY_SERVICES = Arrays.asList(
        "carbon-cache.service",
        "graphite-web.service",
        "i2r-monitoring.service",
        "telegraf.service"
    );

    public SystemConfiguration() {
        this(
            DEFAULT_LOG_LEVEL,
            DEFAULT_SECONDARY_SERVICES,
            DEFAULT_WATCHDOG_INTERVAL_SECS,
            DEFAULT_THREAD_HEARTBEAT_TIMEOUT,
            DEFAULT_THREAD_HEARTBEAT_INTERVAL
        );
    }
}
