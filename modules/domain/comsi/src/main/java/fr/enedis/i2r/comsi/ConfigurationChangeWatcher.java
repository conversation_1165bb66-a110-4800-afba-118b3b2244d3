package fr.enedis.i2r.comsi;

import java.time.Duration;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.params.ConfigurationValue;
import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.system.watchdog.ThreadWatchdog;

/**
 * Le but de cette classe est d'observer tous les changements de configuration du boitier.
 * Cette classe utilise maintenant ThreadWatchdog pour être surveillée par le watchdog.
 */
public class ConfigurationChangeWatcher implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationChangeWatcher.class);
    private static final String THREAD_NAME = "ConfigurationChangeWatcher";

    private DatabaseUpdateWatcherPort databaseUpdateWatcherPort;
    private SiConfigurationNotifierPort siNotifierPort;
    private volatile boolean running = true;

    public ConfigurationChangeWatcher(DatabaseUpdateWatcherPort databaseUpdatePort, SiConfigurationNotifierPort siNotifierPort) {
        this.databaseUpdateWatcherPort = databaseUpdatePort;
        this.siNotifierPort = siNotifierPort;
    }

    @Override
    public void run() {
        try {
            ThreadWatchdog.register(THREAD_NAME, Duration.ofMinutes(2));
            logger.info("Configuration change watcher started");
            ThreadWatchdog.heartbeat(THREAD_NAME, "Started");

            while (running && !Thread.currentThread().isInterrupted()) {
                try {
                    ThreadWatchdog.heartbeat(THREAD_NAME, "Waiting for database updates");
                    List<ConfigurationValue> updatedValues = this.databaseUpdateWatcherPort
                        .waitForUpdates()
                        .stream().filter(confValue -> confValue.parameter().watched)
                        .toList();

                    ThreadWatchdog.heartbeat(THREAD_NAME, "Processing " + updatedValues.size() + " configuration changes");
                    for (ConfigurationValue updatedValue: updatedValues) {
                        this.notifySi(updatedValue);
                    }
                    ThreadWatchdog.heartbeat(THREAD_NAME, "Configuration changes processed");
                } catch (InterruptedException e) {
                    logger.info("Configuration change watcher interrupted, stopping...");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("erreur lors de l'écoute d'un changement de configuration", e);
                    ThreadWatchdog.heartbeat(THREAD_NAME, "Error: " + e.getMessage());
                    // Continue running after error
                }
            }
        } finally {
            ThreadWatchdog.unregister(THREAD_NAME);
            logger.info("Configuration change watcher stopped");
        }
    }

    public void stop() {
        logger.info("Stopping configuration change watcher...");
        running = false;
    }

    private void notifySi(ConfigurationValue updatedConfigurationValue) throws Exception {
        switch (updatedConfigurationValue.parameter()) {
            case BipState -> this.handleStateChange(updatedConfigurationValue.value());
        }
    }

    private void handleStateChange(String newState) throws NumberFormatException, Exception {
        Integer stateValue = Integer.parseInt(newState);

        BipStatus bipStatus = BipStatus.fromStatusCode(stateValue)
            .orElseThrow(() -> new Exception(String.format("nouvel état du boitier invalide: %d", stateValue)));

        this.siNotifierPort.notifyStateChange(bipStatus);
    }
}
