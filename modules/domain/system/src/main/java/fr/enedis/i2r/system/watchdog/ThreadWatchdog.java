package fr.enedis.i2r.system.watchdog;

import java.time.Duration;
import java.time.Instant;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Simple static thread watchdog for monitoring thread heartbeats.
 * This is a minimal implementation that provides all the functionality
 * of the complex class hierarchy with just one class.
 */
public class ThreadWatchdog {

    private static final Logger logger = LoggerFactory.getLogger(ThreadWatchdog.class);

    private static final ConcurrentMap<String, ThreadInfo> threads = new ConcurrentHashMap<>();
    private static Duration defaultTimeout = Duration.ofMinutes(2);

    /**
     * Sets the default timeout for new threads.
     */
    public static void setDefaultTimeout(Duration timeout) {
        defaultTimeout = timeout;
        logger.info("Thread watchdog default timeout set to: {}", timeout);
    }

    /**
     * Registers a thread for monitoring.
     */
    public static void register(String threadName) {
        register(threadName, defaultTimeout);
    }

    /**
     * Registers a thread for monitoring with custom timeout.
     */
    public static void register(String threadName, Duration timeout) {
        threads.put(threadName, new ThreadInfo(timeout, Instant.now(), null));
        logger.info("Thread registered for monitoring: {} (timeout: {})", threadName, timeout);
    }

    /**
     * Records a heartbeat for a thread.
     */
    public static void heartbeat(String threadName) {
        heartbeat(threadName, null);
    }

    /**
     * Records a heartbeat for a thread with status.
     */
    public static void heartbeat(String threadName, String status) {
        ThreadInfo info = threads.get(threadName);
        if (info != null) {
            threads.put(threadName, new ThreadInfo(info.timeout, Instant.now(), status));
            logger.debug("Heartbeat recorded for thread: {} at {}", threadName, Instant.now());
        } else {
            logger.warn("Heartbeat for unregistered thread: {}", threadName);
        }
    }

    /**
     * Unregisters a thread from monitoring.
     */
    public static void unregister(String threadName) {
        ThreadInfo removed = threads.remove(threadName);
        if (removed != null) {
            logger.info("Thread unregistered from monitoring: {}", threadName);
        }
    }

    /**
     * Gets all unhealthy thread names.
     */
    public static Set<String> getUnhealthyThreads() {
        Instant now = Instant.now();
        return threads.entrySet().stream()
            .filter(entry -> {
                ThreadInfo info = entry.getValue();
                Duration timeSinceLastHeartbeat = Duration.between(info.lastHeartbeat, now);
                return timeSinceLastHeartbeat.compareTo(info.timeout) > 0;
            })
            .map(entry -> entry.getKey())
            .collect(Collectors.toSet());
    }

    /**
     * Gets the count of registered threads.
     */
    public static int getThreadCount() {
        return threads.size();
    }

    /**
     * Gets a status summary for all threads, including deadlock detection.
     */
    public static String getStatusSummary() {
        Set<String> unhealthy = getUnhealthyThreads();
        int total = threads.size();

        // Check for deadlocks
        String deadlockStatus = DeadlockDetector.getDeadlockStatus();
        boolean hasDeadlocks = DeadlockDetector.hasDeadlocks();

        if (hasDeadlocks) {
            return String.format("%s | %d threads monitored", deadlockStatus, total);
        } else if (unhealthy.isEmpty()) {
            return String.format("All %d threads healthy | No deadlocks", total);
        } else {
            String unhealthyNames = String.join(", ", unhealthy);
            return String.format("%d/%d threads unhealthy: %s | No deadlocks", unhealthy.size(), total, unhealthyNames);
        }
    }

    /**
     * Checks if any threads are unhealthy.
     */
    public static boolean hasUnhealthyThreads() {
        return !getUnhealthyThreads().isEmpty();
    }

    /**
     * Gets detailed status for a specific thread.
     */
    public static String getThreadStatus(String threadName) {
        ThreadInfo info = threads.get(threadName);
        if (info == null) {
            return "Thread not registered";
        }

        Duration timeSinceLastHeartbeat = Duration.between(info.lastHeartbeat, Instant.now());
        boolean isHealthy = timeSinceLastHeartbeat.compareTo(info.timeout) <= 0;

        String healthStatus = isHealthy ? "Healthy" : "Unhealthy";
        String lastStatus = info.lastStatus != null ? " - " + info.lastStatus : "";

        return String.format("%s - Last heartbeat %d seconds ago%s",
            healthStatus, timeSinceLastHeartbeat.toSeconds(), lastStatus);
    }

    /**
     * Simple record to hold thread information.
     */
    private static record ThreadInfo(Duration timeout, Instant lastHeartbeat, String lastStatus) {}
}
