package fr.enedis.i2r.system.watchdog;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;
import java.util.Arrays;

/**
 * Simple deadlock detector using JVM built-in capabilities.
 * Can be integrated with ThreadWatchdog for comprehensive monitoring.
 */
public class DeadlockDetector {
    
    private static final Logger logger = LoggerFactory.getLogger(DeadlockDetector.class);
    private static final ThreadMXBean threadBean = ManagementFactory.getThreadMXBean();
    
    /**
     * Checks for deadlocks and returns true if any are found.
     */
    public static boolean hasDeadlocks() {
        long[] deadlockedThreads = threadBean.findDeadlockedThreads();
        return deadlockedThreads != null && deadlockedThreads.length > 0;
    }
    
    /**
     * Checks for deadlocks and logs detailed information if found.
     */
    public static boolean checkAndLogDeadlocks() {
        long[] deadlockedThreads = threadBean.findDeadlockedThreads();
        
        if (deadlockedThreads != null && deadlockedThreads.length > 0) {
            logger.error("DEADLOCK DETECTED! {} threads involved: {}", 
                deadlockedThreads.length, Arrays.toString(deadlockedThreads));
            
            // Get thread info for deadlocked threads
            var threadInfos = threadBean.getThreadInfo(deadlockedThreads);
            for (var threadInfo : threadInfos) {
                if (threadInfo != null) {
                    logger.error("Deadlocked thread: {} (ID: {}) - State: {} - Blocked on: {}", 
                        threadInfo.getThreadName(), 
                        threadInfo.getThreadId(),
                        threadInfo.getThreadState(),
                        threadInfo.getLockName());
                }
            }
            return true;
        }
        
        return false;
    }
    
    /**
     * Gets a summary of deadlock status for integration with watchdog.
     */
    public static String getDeadlockStatus() {
        long[] deadlockedThreads = threadBean.findDeadlockedThreads();
        
        if (deadlockedThreads == null || deadlockedThreads.length == 0) {
            return "No deadlocks detected";
        } else {
            return String.format("DEADLOCK: %d threads deadlocked", deadlockedThreads.length);
        }
    }
}
