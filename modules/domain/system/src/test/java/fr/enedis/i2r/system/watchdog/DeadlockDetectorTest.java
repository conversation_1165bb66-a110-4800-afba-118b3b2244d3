package fr.enedis.i2r.system.watchdog;

import org.junit.jupiter.api.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

class DeadlockDetectorTest {

    @Test
    void testNoDeadlocks() {
        assertFalse(DeadlockDetector.hasDeadlocks());
        assertFalse(DeadlockDetector.checkAndLogDeadlocks());
        assertEquals("No deadlocks detected", DeadlockDetector.getDeadlockStatus());
    }

    @Test
    void testDeadlockDetection() throws InterruptedException {
        // Create two locks that will cause a deadlock
        final Object lock1 = new Object();
        final Object lock2 = new Object();
        final CountDownLatch latch = new CountDownLatch(2);
        
        // Thread 1: acquires lock1, then tries to acquire lock2
        Thread thread1 = new Thread(() -> {
            synchronized (lock1) {
                latch.countDown();
                try {
                    // Wait for thread2 to acquire lock2
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
                synchronized (lock2) {
                    // This will never be reached due to deadlock
                }
            }
        }, "DeadlockTest-Thread1");
        
        // Thread 2: acquires lock2, then tries to acquire lock1
        Thread thread2 = new Thread(() -> {
            synchronized (lock2) {
                latch.countDown();
                try {
                    // Wait for thread1 to acquire lock1
                    Thread.sleep(100);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
                synchronized (lock1) {
                    // This will never be reached due to deadlock
                }
            }
        }, "DeadlockTest-Thread2");
        
        // Start both threads
        thread1.start();
        thread2.start();
        
        // Wait for both threads to acquire their first locks
        assertTrue(latch.await(5, TimeUnit.SECONDS));
        
        // Give some time for the deadlock to occur
        Thread.sleep(500);
        
        // Check for deadlocks
        assertTrue(DeadlockDetector.hasDeadlocks(), "Deadlock should be detected");
        assertTrue(DeadlockDetector.checkAndLogDeadlocks(), "checkAndLogDeadlocks should return true");
        
        String status = DeadlockDetector.getDeadlockStatus();
        assertTrue(status.contains("DEADLOCK"), "Status should indicate deadlock: " + status);
        assertTrue(status.contains("threads deadlocked"), "Status should mention deadlocked threads: " + status);
        
        // Clean up - interrupt the threads to break the deadlock
        thread1.interrupt();
        thread2.interrupt();
        
        // Wait for threads to finish
        thread1.join(1000);
        thread2.join(1000);
    }
    
    @Test
    void testDeadlockStatusFormat() {
        String status = DeadlockDetector.getDeadlockStatus();
        assertNotNull(status);
        assertFalse(status.isEmpty());
        
        // Should either be "No deadlocks detected" or "DEADLOCK: X threads deadlocked"
        assertTrue(status.equals("No deadlocks detected") || status.startsWith("DEADLOCK:"));
    }
}
